#!/usr/bin/env python3
"""
测试mask处理修复
"""

import torch
import torch.nn as nn
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mask_handling():
    """测试mask处理是否正确"""
    print("Testing mask handling in SwinTransformerBlock...")

    try:
        # 模拟SwinTransformerBlock的mask处理逻辑
        window_size = 7
        seq_len = window_size * window_size  # 49

        # 模拟不同形状的attn_mask
        print(f"Testing with window_size={window_size}, seq_len={seq_len}")

        # 测试情况1: 3维mask [nW, seq_len, seq_len]
        nW = 64  # 窗口数量
        x_windows_shape_0 = nW * seq_len  # 模拟x_windows.shape[0]

        attn_mask_3d = torch.randn(nW, seq_len, seq_len) * 100
        attn_mask_3d = attn_mask_3d.masked_fill(attn_mask_3d > 0, 0.0).masked_fill(attn_mask_3d <= 0, -100.0)

        print(f"3D mask shape: {attn_mask_3d.shape}")
        print(f"x_windows.shape[0]: {x_windows_shape_0}")

        # 模拟新的处理逻辑
        if attn_mask_3d.dim() == 3:
            mudd_mask = attn_mask_3d.unsqueeze(1)  # [nW, 1, seq_len, seq_len]
            print(f"After unsqueeze: {mudd_mask.shape}")

            # 检查batch维度匹配
            if mudd_mask.shape[0] * seq_len == x_windows_shape_0:
                print("✓ Batch dimension matches correctly")
            else:
                print(f"⚠ Batch dimension mismatch: {mudd_mask.shape[0] * seq_len} != {x_windows_shape_0}")

            if mudd_mask.dtype != torch.bool:
                mudd_mask = (mudd_mask > -50.0)
            print(f"Final mask shape: {mudd_mask.shape}, dtype: {mudd_mask.dtype}")

        # 测试情况2: 2维mask [seq_len, seq_len]
        attn_mask_2d = torch.randn(seq_len, seq_len) * 100
        attn_mask_2d = attn_mask_2d.masked_fill(attn_mask_2d > 0, 0.0).masked_fill(attn_mask_2d <= 0, -100.0)

        print(f"\n2D mask shape: {attn_mask_2d.shape}")

        if attn_mask_2d.dim() == 2:
            mudd_mask = attn_mask_2d.unsqueeze(0).unsqueeze(0)  # [1, 1, seq_len, seq_len]
            print(f"After unsqueeze: {mudd_mask.shape}")

            # 使用新的逻辑计算窗口数量
            nW_calc = x_windows_shape_0 // seq_len
            mudd_mask = mudd_mask.expand(nW_calc, 1, seq_len, seq_len)
            print(f"After expand: {mudd_mask.shape}")

            if mudd_mask.dtype != torch.bool:
                mudd_mask = (mudd_mask > -50.0)
            print(f"Final mask shape: {mudd_mask.shape}, dtype: {mudd_mask.dtype}")

        # 测试情况3: 异常维度的mask (模拟错误情况)
        print(f"\nTesting 5D mask (error case)...")
        attn_mask_5d = torch.randn(1, 1, nW, seq_len, seq_len) * 100
        print(f"5D mask shape: {attn_mask_5d.shape}")

        # 模拟处理异常维度
        if attn_mask_5d.dim() > 4:
            # 重塑为合适的形状
            mask_2d = attn_mask_5d.view(-1, attn_mask_5d.shape[-2], attn_mask_5d.shape[-1])
            nW_calc = x_windows_shape_0 // seq_len
            if mask_2d.shape[0] >= nW_calc:
                mudd_mask = mask_2d[:nW_calc].unsqueeze(1)
            else:
                mudd_mask = mask_2d[0:1].unsqueeze(1).expand(nW_calc, 1, seq_len, seq_len)
            print(f"Reshaped mask shape: {mudd_mask.shape}")

        # 测试情况4: None mask
        print(f"\nTesting None mask...")
        nW_calc = x_windows_shape_0 // seq_len
        mudd_mask = torch.ones(nW_calc, 1, seq_len, seq_len, dtype=torch.bool)
        print(f"None mask shape: {mudd_mask.shape}, dtype: {mudd_mask.dtype}")

        print("✓ Mask handling test passed!")
        return True

    except Exception as e:
        print(f"✗ Mask handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_swin_block_forward():
    """测试SwinTransformerBlock的forward方法"""
    print("\nTesting SwinTransformerBlock forward with mask...")
    
    try:
        from models.swin_transformer import SwinTransformerBlock
        
        # 创建测试参数
        dim = 96
        input_resolution = (56, 56)
        num_heads = 3
        window_size = 7
        
        # 测试无shift的情况
        block = SwinTransformerBlock(
            dim=dim,
            input_resolution=input_resolution,
            num_heads=num_heads,
            window_size=window_size,
            shift_size=0,  # 无shift，attn_mask应该是None
            mlp_ratio=4.0
        )
        
        # 创建测试输入
        batch_size = 2
        seq_len = input_resolution[0] * input_resolution[1]
        x = torch.randn(batch_size, seq_len, dim)
        
        print(f"Input shape: {x.shape}")
        print(f"Block attn_mask is None: {block.attn_mask is None}")
        
        # 前向传播
        output = block(x)
        print(f"Output shape: {output.shape}")
        
        # 测试有shift的情况
        block_shifted = SwinTransformerBlock(
            dim=dim,
            input_resolution=input_resolution,
            num_heads=num_heads,
            window_size=window_size,
            shift_size=3,  # 有shift，attn_mask不应该是None
            mlp_ratio=4.0
        )
        
        print(f"\nShifted block attn_mask is None: {block_shifted.attn_mask is None}")
        if block_shifted.attn_mask is not None:
            print(f"Shifted block attn_mask shape: {block_shifted.attn_mask.shape}")
        
        # 前向传播
        output_shifted = block_shifted(x)
        print(f"Shifted output shape: {output_shifted.shape}")
        
        print("✓ SwinTransformerBlock forward test passed!")
        return True
        
    except Exception as e:
        print(f"✗ SwinTransformerBlock forward test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("Testing mask handling fix")
    print("=" * 60)
    
    tests = [
        test_mask_handling,
        test_swin_block_forward
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All mask tests passed! The bug is fixed.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    main()

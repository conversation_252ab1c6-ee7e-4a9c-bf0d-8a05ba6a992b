#!/usr/bin/env python3
"""
测试batch维度匹配修复
"""

import torch
import torch.nn as nn
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_batch_dimension_matching():
    """测试batch维度匹配是否正确"""
    print("Testing batch dimension matching...")
    
    try:
        # 模拟实际的参数
        window_size = 7
        seq_len = window_size * window_size  # 49
        B = 2  # batch size
        H, W = 56, 56  # input resolution
        nW = (H // window_size) * (W // window_size)  # 8 * 8 = 64 windows
        
        print(f"Parameters:")
        print(f"- window_size: {window_size}")
        print(f"- seq_len: {seq_len}")
        print(f"- batch_size: {B}")
        print(f"- input_resolution: ({H}, {W})")
        print(f"- num_windows: {nW}")
        
        # 模拟x_windows的形状
        x_windows_shape_0 = nW * B  # 64 * 2 = 128
        print(f"- x_windows.shape[0]: {x_windows_shape_0}")
        
        # 测试情况1: 3维mask [nW, seq_len, seq_len]
        print(f"\nTesting 3D mask...")
        attn_mask_3d = torch.randn(nW, seq_len, seq_len) * 100
        attn_mask_3d = attn_mask_3d.masked_fill(attn_mask_3d > 0, 0.0).masked_fill(attn_mask_3d <= 0, -100.0)
        
        print(f"Original mask shape: {attn_mask_3d.shape}")
        
        # 模拟处理逻辑
        if attn_mask_3d.dim() == 3:
            mudd_mask = attn_mask_3d.unsqueeze(1)  # [nW, 1, seq_len, seq_len]
            print(f"After unsqueeze: {mudd_mask.shape}")
            
            # 检查是否需要扩展batch维度
            target_batch_size = x_windows_shape_0
            if mudd_mask.shape[0] != target_batch_size:
                B_calc = target_batch_size // mudd_mask.shape[0]
                print(f"Need to repeat {B_calc} times for batch dimension")
                mudd_mask = mudd_mask.repeat(B_calc, 1, 1, 1)
                print(f"After repeat: {mudd_mask.shape}")
            
            # 验证最终形状
            expected_shape = (x_windows_shape_0, 1, seq_len, seq_len)
            if mudd_mask.shape == expected_shape:
                print(f"✓ Final mask shape correct: {mudd_mask.shape}")
            else:
                print(f"✗ Final mask shape incorrect: {mudd_mask.shape}, expected: {expected_shape}")
        
        # 测试情况2: None mask
        print(f"\nTesting None mask...")
        target_batch_size = x_windows_shape_0
        mudd_mask_none = torch.ones(target_batch_size, 1, seq_len, seq_len, dtype=torch.bool)
        print(f"None mask shape: {mudd_mask_none.shape}")
        
        expected_shape = (x_windows_shape_0, 1, seq_len, seq_len)
        if mudd_mask_none.shape == expected_shape:
            print(f"✓ None mask shape correct: {mudd_mask_none.shape}")
        else:
            print(f"✗ None mask shape incorrect: {mudd_mask_none.shape}, expected: {expected_shape}")
        
        # 测试情况3: 模拟attention计算中的维度匹配
        print(f"\nTesting attention computation dimensions...")
        
        # 模拟q, k, v的形状
        num_heads = 3
        head_dim = 32
        q_shape = (x_windows_shape_0, num_heads, seq_len, head_dim)
        k_shape = (x_windows_shape_0, num_heads, seq_len, head_dim)
        
        print(f"q shape: {q_shape}")
        print(f"k shape: {k_shape}")
        
        # 模拟logits计算
        logits_shape = (x_windows_shape_0, num_heads, seq_len, seq_len)
        print(f"logits shape: {logits_shape}")
        
        # 检查mask是否与logits兼容
        mask_shape = mudd_mask.shape  # (x_windows_shape_0, 1, seq_len, seq_len)
        print(f"mask shape: {mask_shape}")
        
        # 验证torch.where的兼容性
        if (mask_shape[0] == logits_shape[0] and 
            mask_shape[2] == logits_shape[2] and 
            mask_shape[3] == logits_shape[3]):
            print("✓ Mask and logits shapes are compatible for torch.where")
        else:
            print("✗ Mask and logits shapes are NOT compatible for torch.where")
        
        print("✓ Batch dimension matching test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Batch dimension matching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\nTesting edge cases...")
    
    try:
        # 测试不同的batch size和窗口数量组合
        test_cases = [
            {"B": 1, "H": 56, "W": 56, "window_size": 7},
            {"B": 4, "H": 112, "W": 112, "window_size": 7},
            {"B": 2, "H": 224, "W": 224, "window_size": 7},
        ]
        
        for i, case in enumerate(test_cases):
            print(f"\nTest case {i+1}: {case}")
            
            B = case["B"]
            H, W = case["H"], case["W"]
            window_size = case["window_size"]
            seq_len = window_size * window_size
            nW = (H // window_size) * (W // window_size)
            x_windows_shape_0 = nW * B
            
            print(f"  nW: {nW}, x_windows.shape[0]: {x_windows_shape_0}")
            
            # 测试mask处理
            attn_mask = torch.randn(nW, seq_len, seq_len)
            mudd_mask = attn_mask.unsqueeze(1)  # [nW, 1, seq_len, seq_len]
            
            if mudd_mask.shape[0] != x_windows_shape_0:
                B_calc = x_windows_shape_0 // mudd_mask.shape[0]
                mudd_mask = mudd_mask.repeat(B_calc, 1, 1, 1)
            
            expected_shape = (x_windows_shape_0, 1, seq_len, seq_len)
            if mudd_mask.shape == expected_shape:
                print(f"  ✓ Case {i+1} passed: {mudd_mask.shape}")
            else:
                print(f"  ✗ Case {i+1} failed: {mudd_mask.shape}, expected: {expected_shape}")
                return False
        
        print("✓ Edge cases test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Edge cases test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("Testing batch dimension matching fix")
    print("=" * 60)
    
    tests = [
        test_batch_dimension_matching,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All batch dimension tests passed! The bug is fixed.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    main()
